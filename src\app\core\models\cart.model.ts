// API Response models
export interface ApiCartItem {
  id: number;
  user: number;
  game: number;
  game_title: string;
  quantity: number;
  added_at: string;
}

export interface ApiCartResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ApiCartItem[];
}

// Frontend models (enriched with game data)
export interface CartItem {
  id: number;
  user: number;
  game: number;
  game_title: string;
  quantity: number;
  added_at: string;
  // Enriched game data
  game_data?: {
    title: string;
    price: string;
    cover_image: string | null;
    trial_available: boolean;
    requires_device: boolean;
  };
}

export interface Cart {
  items: CartItem[];
  total_items: number;
  total_price: number;
}

// API Request models
export interface AddToCartRequest {
  game: number;
  quantity?: number;
}

export interface UpdateCartRequest {
  game: number;
  quantity: number;
}

export interface CartError {
  non_field_errors?: string[];
  quantity?: string[];
  game?: string[];
}

// Purchase models for checkout
export interface Purchase {
  id: number;
  game_title: string;
  purchase_type: string;
  price: string;
  status: string;
  created_at: string;
}

export interface CheckoutResponse {
  purchases: Purchase[];
}

export interface CheckoutError {
  non_field_errors?: string[];
  detail?: string;
}

// Purchase API models
export interface PurchaseResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Purchase[];
}

export interface PaymentRequest {
  access_type: 'oneday' | 'subscription';
}

export interface PaymentResponse {
  detail: string;
}

export interface PaymentError {
  detail?: string;
  non_field_errors?: string[];
}

export interface PurchaseFilters {
  status?: 'paid' | 'pending' | 'failed';
  ordering?: string;
  search?: string;
}

// Access type selection interfaces
export interface AccessType {
  id: 'oneday' | 'subscription';
  name: string;
  description: string;
  duration: string;
  icon: string;
}

export interface AccessTypeSelection {
  purchaseId: number;
  gameTitle: string;
  price: string;
  accessType?: 'oneday' | 'subscription';
}
