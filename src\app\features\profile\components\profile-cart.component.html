<!-- Profile Cart -->
<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-black text-white mb-2">
        Корзина
      </h1>
      <p class="text-gray-300">
        {{ cart.total_items }} {{ cart.total_items === 1 ? 'товар' : cart.total_items < 5 ? 'товара' : 'товаров' }}
      </p>
    </div>
    
    <button
      (click)="goToGames()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Продолжить покупки
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="text-center py-12">
    <div class="text-red-400 mb-4">{{ error }}</div>
    <button
      (click)="loadCart()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty Cart -->
  <div *ngIf="!loading && !error && cart.items.length === 0" class="text-center py-12">
    <div class="text-gray-400 mb-6">
      <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
      </svg>
      <p class="text-xl">Ваша корзина пуста</p>
    </div>
    <button
      (click)="goToGames()"
      class="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-lg font-medium"
    >
      Перейти к играм
    </button>
  </div>

  <!-- Cart Items -->
  <div *ngIf="!loading && !error && cart.items.length > 0" class="space-y-6">
    <!-- Cart Items List -->
    <div class="space-y-4">
      <div 
        *ngFor="let item of cart.items" 
        class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6 hover:border-slate-500/60 transition-all"
      >
        <div class="flex flex-col md:flex-row gap-6">
          <!-- Game Image -->
          <div class="w-full md:w-32 h-32 bg-slate-700 rounded-lg overflow-hidden flex-shrink-0">
            <img 
              *ngIf="getGameData(item.game)?.cover_image" 
              [src]="getGameData(item.game)?.cover_image" 
              [alt]="item.game_title"
              class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="viewGameDetails(item.game)"
            >
            <div 
              *ngIf="!getGameData(item.game)?.cover_image"
              class="w-full h-full flex items-center justify-center text-gray-400"
            >
              Нет изображения
            </div>
          </div>

          <!-- Game Info -->
          <div class="flex-1">
            <div class="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
              <div class="flex-1">
                <h3 
                  class="text-xl font-bold text-white mb-2 cursor-pointer hover:text-blue-400 transition-colors"
                  (click)="viewGameDetails(item.game)"
                >
                  {{ item.game_title }}
                </h3>
                <p *ngIf="getGameData(item.game)?.description" class="text-gray-300 text-sm mb-4 line-clamp-2">
                  {{ getGameData(item.game)?.description }}
                </p>
                <div class="text-blue-400 font-bold text-lg">
                  {{ getGameData(item.game)?.price || '0' }}₽ за единицу
                </div>
              </div>

              <!-- Quantity and Actions -->
              <div class="flex flex-col md:items-end gap-4">
                <!-- Quantity Controls -->
                <div class="flex items-center gap-3">
                  <span class="text-gray-300 text-sm">Количество:</span>
                  <div class="flex items-center gap-2">
                    <button
                      (click)="updateQuantity(item, item.quantity - 1)"
                      [disabled]="item.quantity <= 1"
                      class="w-8 h-8 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded flex items-center justify-center transition-colors"
                    >
                      -
                    </button>
                    <span class="w-12 text-center text-white font-medium">{{ item.quantity }}</span>
                    <button
                      (click)="updateQuantity(item, item.quantity + 1)"
                      class="w-8 h-8 bg-slate-700 hover:bg-slate-600 text-white rounded flex items-center justify-center transition-colors"
                    >
                      +
                    </button>
                  </div>
                </div>

                <!-- Total Price -->
                <div class="text-right">
                  <div class="text-gray-400 text-sm">Итого:</div>
                  <div class="text-blue-400 font-bold text-xl">{{ getItemTotal(item) }}₽</div>
                </div>

                <!-- Remove Button -->
                <button
                  (click)="removeItem(item)"
                  class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
                >
                  Удалить
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cart Summary -->
    <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <div class="text-gray-300 mb-2">
            Всего товаров: {{ cart.total_items }}
          </div>
          <div class="text-2xl font-bold text-white">
            Итого: <span class="text-blue-400">{{ cart.total_price }}₽</span>
          </div>
        </div>

        <div class="flex gap-4">
          <button
            (click)="clearCart()"
            [disabled]="cart.items.length === 0"
            class="px-6 py-3 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            Очистить корзину
          </button>
          
          <button
            (click)="checkout()"
            [disabled]="cart.items.length === 0 || checkoutLoading"
            class="px-8 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center gap-2"
          >
            <span *ngIf="checkoutLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            {{ checkoutLoading ? 'Оформление...' : 'Оформить заказ' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
