<!-- Game Detail Modal -->
<div *ngIf="isVisible" class="fixed inset-0 z-50 overflow-y-auto" (click)="closeModal()">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 transition-opacity bg-black bg-opacity-75"></div>

    <!-- Modal panel -->
    <div 
      class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-slate-900 shadow-xl rounded-2xl border border-slate-700"
      (click)="$event.stopPropagation()">
      
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-bold text-white">Детали игры</h2>
        <div class="flex space-x-2">
          <!-- Edit Button -->
          <button
            *ngIf="!isEditing && game"
            (click)="toggleEdit()"
            class="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Редактировать
          </button>

          <!-- Delete Button -->
          <button
            *ngIf="!isEditing && game"
            (click)="confirmDelete()"
            class="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Удалить
          </button>

          <!-- Close Button -->
          <button
            (click)="closeModal()"
            class="px-3 py-1 text-sm bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Закрыть
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
        <h4 class="text-red-300 font-semibold mb-2 text-sm">Ошибка загрузки</h4>
        <p class="text-red-200 mb-3 text-sm">{{ error }}</p>
        <button (click)="loadGame()" class="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
          Попробовать снова
        </button>
      </div>

      <!-- Game Content -->
      <div *ngIf="game && !loading && !error">
        <!-- View Mode -->
        <div *ngIf="!isEditing" class="space-y-6">
          <!-- Game Header -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Cover Image -->
            <div class="lg:col-span-1">
              <div class="aspect-square bg-slate-800 rounded-lg overflow-hidden border border-slate-700">
                <img 
                  *ngIf="game.cover_image" 
                  [src]="game.cover_image" 
                  [alt]="game.title"
                  class="w-full h-full object-cover">
                <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center text-gray-400">
                  <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Game Info -->
            <div class="lg:col-span-2 space-y-3">
              <div>
                <h1 class="text-2xl font-bold text-white mb-2">{{ game.title }}</h1>
                <p *ngIf="game.subtitle" class="text-lg text-gray-300 mb-3">{{ game.subtitle }}</p>

                <!-- Price and Badges -->
                <div class="flex flex-wrap items-center gap-2 mb-3">
                  <span class="text-lg font-bold text-green-400">{{ formatPrice(game.price) }} ₸</span>
                  <span *ngIf="game.trial_available" class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                    Пробная версия
                  </span>
                  <span *ngIf="game.requires_device" class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">
                    Требует устройство
                  </span>
                </div>

                <!-- Creation Date -->
                <p class="text-gray-400 text-xs">
                  Создано: {{ formatDate(game.created_at) }}
                </p>
              </div>

              <!-- Description -->
              <div>
                <h3 class="text-base font-semibold text-white mb-2">Описание</h3>
                <p class="text-gray-300 leading-relaxed text-sm">{{ game.description }}</p>
              </div>
            </div>
          </div>

          <!-- Additional Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- How to Play -->
            <div *ngIf="game.how_to_play">
              <h3 class="text-base font-semibold text-white mb-2">Как играть</h3>
              <div class="bg-slate-800/60 rounded-lg p-3 border border-slate-700">
                <p class="text-gray-300 leading-relaxed text-sm">{{ game.how_to_play }}</p>
              </div>
            </div>

            <!-- Target Audience -->
            <div *ngIf="game.target_audience">
              <h3 class="text-base font-semibold text-white mb-2">Целевая аудитория</h3>
              <div class="bg-slate-800/60 rounded-lg p-3 border border-slate-700">
                <p class="text-gray-300 text-sm">{{ game.target_audience }}</p>
              </div>
            </div>

            <!-- System Requirements -->
            <div *ngIf="game.system_requirements">
              <h3 class="text-base font-semibold text-white mb-2">Системные требования</h3>
              <div class="bg-slate-800/60 rounded-lg p-3 border border-slate-700">
                <p class="text-gray-300 leading-relaxed text-sm">{{ game.system_requirements }}</p>
              </div>
            </div>

            <!-- Required Equipment -->
            <div *ngIf="game.required_equipment">
              <h3 class="text-base font-semibold text-white mb-2">Необходимое оборудование</h3>
              <div class="bg-slate-800/60 rounded-lg p-3 border border-slate-700">
                <p class="text-gray-300 leading-relaxed text-sm">{{ game.required_equipment }}</p>
              </div>
            </div>
          </div>

          <!-- Gallery Images -->
          <div *ngIf="game.gallery_items && game.gallery_items.length > 0" class="mt-6">
            <h3 class="text-base font-semibold text-white mb-3">Галерея ({{ game.gallery_items.length }})</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div *ngFor="let item of game.gallery_items" class="aspect-video bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-colors">
                <img [src]="item.file" [alt]="game.title + ' gallery image'" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300 cursor-pointer">
              </div>
            </div>
          </div>
        </div>

        <!-- Edit Mode -->
        <div *ngIf="isEditing" class="space-y-6">
          <!-- Edit Error Message -->
          <div *ngIf="editError" class="bg-red-500/20 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg">
            {{ editError }}
          </div>

          <form (ngSubmit)="saveChanges()" class="space-y-4">
            <!-- Title and Subtitle -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Название игры *</label>
                <input
                  type="text"
                  [(ngModel)]="editGame.title"
                  name="title"
                  required
                  class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>

              <div>
                <label class="block text-gray-300 text-sm font-medium mb-2">Подзаголовок</label>
                <input
                  type="text"
                  [(ngModel)]="editGame.subtitle"
                  name="subtitle"
                  class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Описание *</label>
              <textarea
                [(ngModel)]="editGame.description"
                name="description"
                required
                rows="4"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- Price and Checkboxes -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-gray-300 text-xs font-medium mb-1">Цена (₸) *</label>
                <input
                  type="text"
                  [(ngModel)]="editGame.price"
                  name="price"
                  required
                  pattern="[0-9]+(\.[0-9]{1,2})?"
                  class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>

              <div class="flex items-center pt-8">
                <input
                  type="checkbox"
                  [(ngModel)]="editGame.trial_available"
                  name="trial_available"
                  id="edit_trial_available"
                  class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
                <label for="edit_trial_available" class="ml-2 text-gray-300 text-sm">Пробная версия</label>
              </div>

              <div class="flex items-center pt-8">
                <input
                  type="checkbox"
                  [(ngModel)]="editGame.requires_device"
                  name="requires_device"
                  id="edit_requires_device"
                  class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
                <label for="edit_requires_device" class="ml-2 text-gray-300 text-sm">Требует устройство</label>
              </div>
            </div>

            <!-- Target Audience -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Целевая аудитория</label>
              <input
                type="text"
                [(ngModel)]="editGame.target_audience"
                name="target_audience"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <!-- Cover Image -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Обложка игры</label>
              <input
                type="file"
                (change)="onCoverImageSelected($event)"
                accept="image/*"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700 file:cursor-pointer">
              <p class="text-gray-400 text-xs mt-1">Поддерживаются форматы: JPG, PNG, GIF. Максимальный размер: 5MB</p>
            </div>

            <!-- Gallery Images -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Галерея изображений и видео</label>

              <!-- Existing Gallery Images -->
              <div *ngIf="game && game.gallery_items && game.gallery_items.length > 0" class="mb-4">
                <h5 class="text-gray-300 text-sm font-medium mb-2">Текущие изображения ({{ game.gallery_items.length }}):</h5>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <div *ngFor="let item of game.gallery_items" class="relative group">
                    <div class="aspect-video bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden">
                      <img [src]="item.file" [alt]="'Gallery image ' + item.id" class="w-full h-full object-cover">
                    </div>
                    <button
                      type="button"
                      (click)="removeExistingGalleryImage(item.id)"
                      class="absolute top-2 right-2 bg-red-600/80 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Add New Gallery Files -->
              <input
                type="file"
                (change)="onGalleryFilesSelected($event)"
                accept="image/*,video/*"
                multiple
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-green-600 file:text-white hover:file:bg-green-700 file:cursor-pointer">
              <p class="text-gray-400 text-xs mt-1">Поддерживаются форматы: JPG, PNG, GIF, MP4, WebM. Максимальный размер: 5MB для изображений, 10MB для видео</p>

              <!-- Selected New Gallery Files Preview -->
              <div *ngIf="selectedGalleryFiles.length > 0" class="mt-4">
                <h5 class="text-gray-300 text-sm font-medium mb-2">Новые файлы для добавления ({{ selectedGalleryFiles.length }}):</h5>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                  <div *ngFor="let file of selectedGalleryFiles; let i = index" class="flex items-center justify-between bg-slate-800/40 border border-slate-600/50 rounded-lg p-3">
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-slate-600 rounded flex items-center justify-center">
                        <svg *ngIf="file.type.startsWith('image/')" class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                        </svg>
                        <svg *ngIf="file.type.startsWith('video/')" class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                      <div>
                        <p class="text-gray-300 text-sm font-medium">{{ file.name }}</p>
                        <p class="text-gray-400 text-xs">{{ (file.size / 1024 / 1024).toFixed(2) }} MB</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      (click)="removeGalleryFile(i)"
                      class="text-red-400 hover:text-red-300 transition-colors">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Gallery Upload Progress -->
              <div *ngIf="galleryUploadLoading" class="mt-4">
                <div class="flex items-center space-x-2 mb-2">
                  <svg class="animate-spin w-4 h-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-blue-400 text-sm">Загрузка файлов галереи...</span>
                </div>
              </div>
            </div>

            <!-- How to Play -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Как играть</label>
              <textarea
                [(ngModel)]="editGame.how_to_play"
                name="how_to_play"
                rows="3"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- System Requirements -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Системные требования</label>
              <textarea
                [(ngModel)]="editGame.system_requirements"
                name="system_requirements"
                rows="3"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- Required Equipment -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Необходимое оборудование</label>
              <textarea
                [(ngModel)]="editGame.required_equipment"
                name="required_equipment"
                rows="2"
                class="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex space-x-2 pt-2">
              <button
                type="submit"
                [disabled]="editLoading || galleryUploadLoading"
                class="flex-1 px-3 py-2 text-xs bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-medium rounded-lg transition-all duration-300 disabled:cursor-not-allowed">
                <span *ngIf="!editLoading && !galleryUploadLoading">Сохранить изменения</span>
                <span *ngIf="galleryUploadLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Загрузка галереи...
                </span>
                <span *ngIf="editLoading && !galleryUploadLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Сохранение...
                </span>
              </button>

              <button
                type="button"
                (click)="toggleEdit()"
                [disabled]="editLoading || galleryUploadLoading"
                class="px-4 py-2 text-xs bg-slate-600 hover:bg-slate-700 disabled:bg-slate-700 text-white font-medium rounded-lg transition-all duration-300 disabled:cursor-not-allowed">
                Отменить
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div *ngIf="showDeleteConfirm" class="fixed inset-0 z-60 overflow-y-auto">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 transition-opacity bg-black bg-opacity-75"></div>

    <!-- Confirmation panel -->
    <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-slate-900 shadow-xl rounded-2xl border border-slate-700">
      <div class="flex items-center mb-4">
        <div class="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
          <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
      </div>
      
      <div class="text-center">
        <h3 class="text-base font-medium text-white mb-2">Удалить игру</h3>
        <p class="text-gray-300 mb-4 text-sm">
          Вы уверены, что хотите удалить игру "{{ game?.title }}"? Это действие нельзя отменить.
        </p>

        <div class="flex space-x-3">
          <button
            (click)="deleteGame()"
            [disabled]="deleteLoading"
            class="flex-1 px-3 py-2 text-sm bg-red-600 hover:bg-red-700 disabled:bg-red-700 text-white font-medium rounded-lg transition-colors disabled:cursor-not-allowed">
            <span *ngIf="!deleteLoading">Удалить</span>
            <span *ngIf="deleteLoading">Удаление...</span>
          </button>

          <button
            (click)="cancelDelete()"
            [disabled]="deleteLoading"
            class="flex-1 px-3 py-2 text-sm bg-slate-600 hover:bg-slate-700 disabled:bg-slate-700 text-white font-medium rounded-lg transition-colors disabled:cursor-not-allowed">
            Отменить
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
